#!/usr/bin/env python3
"""
Migration script to add Reddit-style features to the database.
This script adds the Comment and Vote tables and updates existing tables.
"""

from app import app
from extensions import db
from models import User, Post, Comment, Vote
import sys

def migrate_database():
    """Run the database migration"""
    print("Starting Reddit-style features migration...")
    
    with app.app_context():
        try:
            # Create all new tables
            print("Creating new tables...")
            db.create_all()
            
            print("Migration completed successfully!")
            print("\nNew tables created:")
            print("- Comment (for threaded discussions)")
            print("- Vote (for upvote/downvote system)")
            print("\nUpdated tables:")
            print("- Post (added relationships)")
            print("- User (added relationships)")
            
        except Exception as e:
            print(f"Migration failed: {e}")
            sys.exit(1)

if __name__ == "__main__":
    migrate_database()
