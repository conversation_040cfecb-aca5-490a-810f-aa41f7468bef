from flask import Blueprint, render_template, redirect, flash, request, url_for
from flask_login import login_required, current_user
from models import Application, InviteCode, User, Board, BoardRequest
from routes.auth import generate_invite_code, send_invite_email
from extensions import db, login_manager
import datetime


admin_bp = Blueprint("admin", __name__)

def admin_required(f):
    from functools import wraps
    from flask import abort
    @wraps(f)
    def decorated(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated

@admin_bp.route("/applications")
@login_required
@admin_required
def view_applications():
    apps = Application.query.filter_by(status="pending").all()
    return render_template("admin_dashboard.html", applications=apps)

@admin_bp.route("/application/<int:app_id>/approve")
@login_required
@admin_required
def approve_application(app_id):
    app_entry = Application.query.get(app_id)
    if not app_entry:
        flash("Application not found")
        return redirect("/admin/applications")

    app_entry.status = "approved"
    code_str = generate_invite_code()
    invite = InviteCode(code=code_str, email=app_entry.email)
    db.session.add(invite)
    db.session.commit()

    # Best-effort email notification (prints if mail not configured)
    try:
        send_invite_email(app_entry.email, code_str)
    except Exception:
        pass

    flash("Application approved & invite code sent")
    return redirect("/admin/applications")

@admin_bp.route("/application/<int:app_id>/deny")
@login_required
@admin_required
def deny_application(app_id):
    app_entry = Application.query.get(app_id)
    if app_entry:
        app_entry.status = "denied"
        db.session.commit()
    flash("Application denied")
    return redirect("/admin/applications")



@admin_bp.route("/members")
@login_required
@admin_required
def members():
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template("admin_members.html", users=users, now=datetime.datetime.utcnow())


@admin_bp.route("/member/<int:user_id>/suspend")
@login_required
@admin_required
def suspend_member(user_id):
    user = User.query.get_or_404(user_id)
    if user.is_admin:
        flash("Cannot suspend an admin user.", "error")
        return redirect("/admin/members")
    user.is_suspended = True
    db.session.commit()
    flash(f"User {user.username} suspended.", "success")
    return redirect("/admin/members")


@admin_bp.route("/member/<int:user_id>/reactivate")
@login_required
@admin_required
def reactivate_member(user_id):
    user = User.query.get_or_404(user_id)
    user.is_suspended = False
    db.session.commit()
    flash(f"User {user.username} reactivated.", "success")
    return redirect("/admin/members")


@admin_bp.route("/settings/boards")
@login_required
@admin_required
def manage_boards():
    boards = Board.query.order_by(Board.name.asc()).all()
    return render_template("admin_settings.html", boards=boards)


@admin_bp.route("/settings/boards/add", methods=["POST"])
@login_required
@admin_required
def add_board():
    name = (request.form.get("name") or "").strip()
    description = (request.form.get("description") or "").strip()
    if not name:
        flash("Board name is required.", "error")
        return redirect("/admin/settings/boards")
    if Board.query.filter_by(name=name).first():
        flash("Board with that name already exists.", "error")
        return redirect("/admin/settings/boards")
    board = Board(name=name, description=description)
    db.session.add(board)
    db.session.commit()
    flash("Board added.", "success")
    return redirect("/admin/settings/boards")


@admin_bp.route("/settings/boards/<int:board_id>/toggle")
@login_required
@admin_required
def toggle_board(board_id):
    board = Board.query.get_or_404(board_id)
    board.is_active = not board.is_active
    db.session.commit()
    flash(f"Board '{board.name}' {'activated' if board.is_active else 'deactivated'}.", "success")
    return redirect("/admin/settings/boards")


@admin_bp.route("/settings/boards/<int:board_id>/delete")
@login_required
@admin_required
def delete_board(board_id):
    board = Board.query.get_or_404(board_id)
    db.session.delete(board)
    db.session.commit()
    flash("Board deleted.", "success")
    return redirect("/admin/settings/boards")



@admin_bp.route("/board-requests")
@login_required
@admin_required
def view_board_requests():
    """View pending board creation requests and show actions to approve or deny."""
    requests = BoardRequest.query.filter_by(status="pending").order_by(BoardRequest.created_at.desc()).all()
    user_map = {}
    if requests:
        user_ids = {r.user_id for r in requests}
        users = User.query.filter(User.id.in_(user_ids)).all()
        user_map = {u.id: u for u in users}
    return render_template("admin_board_requests.html", requests=requests, user_map=user_map)


@admin_bp.route("/board-requests/<int:req_id>/approve")
@login_required
@admin_required
def approve_board_request(req_id: int):
    """Approve a board creation request: create the board and mark as approved."""
    req = BoardRequest.query.get(req_id)
    if not req:
        flash("Board request not found", "error")
        return redirect(url_for("admin.view_board_requests"))

    # Prevent duplicates if board already exists (could have been created elsewhere)
    if Board.query.filter_by(name=req.name).first():
        flash(f"A board named '{req.name}' already exists.", "error")
        return redirect(url_for("admin.view_board_requests"))

    board = Board(name=req.name, description=req.description)
    db.session.add(board)
    req.status = "approved"
    db.session.commit()
    flash(f"Board '{req.name}' created and request approved.", "success")
    return redirect(url_for("admin.view_board_requests"))


@admin_bp.route("/board-requests/<int:req_id>/deny")
@login_required
@admin_required
def deny_board_request(req_id: int):
    """Deny a board creation request."""
    req = BoardRequest.query.get(req_id)
    if not req:
        flash("Board request not found", "error")
        return redirect(url_for("admin.view_board_requests"))
    req.status = "denied"
    db.session.commit()
    flash("Board request denied.", "info")
    return redirect(url_for("admin.view_board_requests"))
