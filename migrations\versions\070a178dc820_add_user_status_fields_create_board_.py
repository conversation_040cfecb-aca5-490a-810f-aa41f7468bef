"""Add user status fields; create Board model

Revision ID: 070a178dc820
Revises: ff4cb12469bd
Create Date: 2025-09-17 14:10:29.779373

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '070a178dc820'
down_revision = 'ff4cb12469bd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('board', schema=None) as batch_op:
        batch_op.drop_column('used')
        batch_op.drop_column('used_at')

    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.drop_column('updated_at')

    with op.batch_alter_table('post', schema=None) as batch_op:
        batch_op.alter_column('author_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
        batch_op.alter_column('content',
               existing_type=sa.TEXT(),
               nullable=False)

    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_suspended', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('subscription_expires_at', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('subscription_expires_at')
        batch_op.drop_column('is_suspended')

    with op.batch_alter_table('post', schema=None) as batch_op:
        batch_op.alter_column('content',
               existing_type=sa.TEXT(),
               nullable=True)
        batch_op.alter_column('title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.alter_column('author_id',
               existing_type=sa.INTEGER(),
               nullable=True)

    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.add_column(sa.Column('updated_at', sa.DATETIME(), nullable=True))

    with op.batch_alter_table('board', schema=None) as batch_op:
        batch_op.add_column(sa.Column('used_at', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('used', sa.BOOLEAN(), nullable=True))

    # ### end Alembic commands ###
