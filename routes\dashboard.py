from flask import Blueprint, render_template, request, redirect, flash, jsonify, url_for
from flask_login import login_required, current_user
from models import Post, Comment, Vote, User, Board, BoardRequest
from extensions import db
from sqlalchemy import desc, func
import datetime
import bleach

# Allowed HTML tags and attributes for rich text content
ALLOWED_TAGS = [
    'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3',
    'ul', 'ol', 'li', 'blockquote', 'code', 'pre', 'a'
]

ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    '*': ['class']
}

def sanitize_html(content):
    """Sanitize HTML content to prevent XSS attacks"""
    if not content:
        return ""
    return bleach.clean(content, tags=ALLOWED_TAGS, attributes=ALLOWED_ATTRIBUTES, strip=True)

def time_ago(timestamp):
    """Convert timestamp to relative time string"""
    if not timestamp:
        return "unknown"

    now = datetime.datetime.utcnow()
    diff = now - timestamp

    if diff.total_seconds() < 60:
        return "just now"
    elif diff.total_seconds() < 3600:
        return f"{int(diff.total_seconds() / 60)}m ago"
    elif diff.total_seconds() < 86400:
        return f"{int(diff.total_seconds() / 3600)}h ago"
    elif diff.total_seconds() < 2592000:
        return f"{int(diff.total_seconds() / 86400)}d ago"
    else:
        return timestamp.strftime('%b %d, %Y')

dash_bp = Blueprint("dash", __name__)

@dash_bp.route("/dashboard")
@login_required
def dashboard():
    # Get sorting preference and board filter from query params
    sort_by = request.args.get('sort', 'hot')  # hot, new, top
    active_board = request.args.get('board')

    # Load active boards for header/cards
    boards = Board.query.filter_by(is_active=True).order_by(Board.name.asc()).all()

    # Base query (optionally filtered by board)
    base_query = Post.query
    if active_board:
        base_query = base_query.filter_by(board=active_board)

    if sort_by == 'new':
        posts = base_query.order_by(Post.created_at.desc()).all()
    elif sort_by == 'top':
        posts = base_query.all()
        posts.sort(key=lambda p: p.vote_score, reverse=True)
    else:  # hot (default)
        posts = base_query.order_by(Post.created_at.desc()).all()
        posts.sort(key=lambda p: p.vote_score + (1 if (datetime.datetime.utcnow() - p.created_at).days < 1 else 0), reverse=True)

    return render_template("board.html", posts=posts, sort_by=sort_by, time_ago=time_ago, boards=boards, active_board=active_board)

@dash_bp.route("/post/new", methods=["GET"])
@login_required
def new_post():
    """Render Create Post page with live preview and board selector"""
    selected_board = request.args.get("board") or "General"
    boards = Board.query.filter_by(is_active=True).order_by(Board.name.asc()).all()
    return render_template("create_post.html", boards=boards, selected_board=selected_board)


@dash_bp.route("/post/create", methods=["POST"])
@login_required
def create_post():
    # Get and sanitize form data
    title = request.form.get("title", "").strip()
    content = request.form.get("content", "").strip()

    if not title or not content:
        flash("Title and content are required.", "error")
        return redirect(url_for("dash.dashboard"))

    # Sanitize HTML content
    sanitized_content = sanitize_html(content)

    if not sanitized_content or len(sanitized_content.strip()) < 10:
        flash("Content must be at least 10 characters long.", "error")
        return redirect(url_for("dash.dashboard"))

    new_post = Post(
        author_id=current_user.id,
        board=request.form.get("board", "General"),
        title=title,
        content=sanitized_content
    )
    db.session.add(new_post)
    db.session.commit()
    flash("Post created successfully!", "success")
    return redirect(url_for("dash.dashboard"))

@dash_bp.route("/post/<int:post_id>")
@login_required
def view_post(post_id):
    """View individual post with all comments"""
    post = Post.query.get_or_404(post_id)

    # Get top-level comments sorted by vote score
    comments = Comment.query.filter_by(post_id=post_id, parent_id=None)\
                           .order_by(desc(Comment.created_at)).all()

    # Sort comments by vote score
    comments.sort(key=lambda c: c.vote_score, reverse=True)

    return render_template("post_detail.html", post=post, comments=comments, time_ago=time_ago)

@dash_bp.route("/comment/create", methods=["POST"])
@login_required
def create_comment():
    """Create a new comment or reply"""
    post_id = request.form.get("post_id")
    parent_id = request.form.get("parent_id")  # None for top-level comments
    content = request.form.get("content", "").strip()

    if not post_id or not content:
        flash("Missing required fields", "error")
        return redirect(request.referrer or url_for("dash.dashboard"))

    # Sanitize content (comments use plain text, not rich HTML)
    sanitized_content = bleach.clean(content, tags=[], strip=True)

    if not sanitized_content or len(sanitized_content.strip()) < 1:
        flash("Comment cannot be empty", "error")
        return redirect(request.referrer or url_for("dash.dashboard"))

    # Validate post exists
    Post.query.get_or_404(post_id)

    # Validate parent comment if replying
    if parent_id:
        parent_comment = Comment.query.get_or_404(parent_id)
        # Limit nesting depth to 4 levels
        if parent_comment.depth >= 3:
            flash("Maximum reply depth reached", "error")
            return redirect(request.referrer)

    new_comment = Comment(
        author_id=current_user.id,
        post_id=post_id,
        parent_id=parent_id if parent_id else None,
        content=sanitized_content
    )

    db.session.add(new_comment)
    db.session.commit()

    flash("Comment posted successfully!", "success")
    return redirect(request.referrer or url_for("dash.view_post", post_id=post_id))

@dash_bp.route("/vote", methods=["POST"])
@login_required
def vote():
    """Handle voting on posts and comments via AJAX"""
    data = request.get_json()

    if not data:
        return jsonify({"error": "No data provided"}), 400

    vote_type = data.get("vote_type")  # 'upvote' or 'downvote'
    post_id = data.get("post_id")
    comment_id = data.get("comment_id")

    if vote_type not in ['upvote', 'downvote']:
        return jsonify({"error": "Invalid vote type"}), 400

    if not post_id and not comment_id:
        return jsonify({"error": "Must specify post_id or comment_id"}), 400

    if post_id and comment_id:
        return jsonify({"error": "Cannot vote on both post and comment"}), 400

    # Find existing vote
    existing_vote = None
    if post_id:
        existing_vote = Vote.query.filter_by(user_id=current_user.id, post_id=post_id).first()
        target = Post.query.get_or_404(post_id)
    else:
        existing_vote = Vote.query.filter_by(user_id=current_user.id, comment_id=comment_id).first()
        target = Comment.query.get_or_404(comment_id)

    if existing_vote:
        if existing_vote.vote_type == vote_type:
            # Remove vote if clicking same button
            db.session.delete(existing_vote)
            db.session.commit()
            return jsonify({
                "success": True,
                "action": "removed",
                "vote_score": target.vote_score
            })
        else:
            # Change vote type
            existing_vote.vote_type = vote_type
            db.session.commit()
            return jsonify({
                "success": True,
                "action": "changed",
                "vote_type": vote_type,
                "vote_score": target.vote_score
            })
    else:
        # Create new vote
        new_vote = Vote(
            user_id=current_user.id,
            post_id=post_id,
            comment_id=comment_id,
            vote_type=vote_type
        )
        db.session.add(new_vote)
        db.session.commit()
        return jsonify({
            "success": True,
            "action": "created",
            "vote_type": vote_type,
            "vote_score": target.vote_score
        })

@dash_bp.route("/boards/request", methods=["GET", "POST"])
@login_required
def request_board():
    if request.method == "POST":
        name = (request.form.get("name") or "").strip()
        description = (request.form.get("description") or "").strip()
        reason = (request.form.get("reason") or "").strip()
        if not name:
            flash("Board name is required.", "error")
            return redirect(url_for("dash.request_board"))
        # Prevent duplicates if a board already exists
        if Board.query.filter_by(name=name).first():
            flash("A board with that name already exists.", "error")
            return redirect(url_for("dash.request_board"))
        # Prevent spamming duplicate pending requests by same user
        existing_req = BoardRequest.query.filter_by(user_id=current_user.id, name=name, status="pending").first()
        if existing_req:
            flash("You already have a pending request for this board.", "info")
            return redirect(url_for("dash.request_board"))
        br = BoardRequest(user_id=current_user.id, name=name, description=description, reason=reason)
        db.session.add(br)
        db.session.commit()
        flash("Your board request has been submitted for review.", "success")
        return redirect(url_for("dash.dashboard"))
    return render_template("board_request.html")
