from app import app
from models import db, User
from werkzeug.security import generate_password_hash

with app.app_context():
    email = "<EMAIL>"
    username = "The Founder"
    password = "thefoundersecretpass123"  # Change to secure
    existing = User.query.filter_by(email=email).first()
    if existing:
        print("Admin already exists")
    else:
        admin_user = User(
            email=email,
            username=username,
            password_hash=generate_password_hash(password),
            tier="The Founder",
            is_admin=True
        )
        db.session.add(admin_user)
        db.session.commit()
        print("Admin account created")
