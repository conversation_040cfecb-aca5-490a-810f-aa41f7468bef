{% extends "layout.html" %}

{% macro render_vote_buttons(item, item_type) %}
<div class="vote-buttons" data-{{ item_type }}-id="{{ item.id }}">
  {% set user_vote = item.get_user_vote(current_user.id) if current_user.is_authenticated else None %}
  <button class="vote-btn upvote {{ 'active' if user_vote and user_vote.vote_type == 'upvote' else '' }}"
          data-vote-type="upvote" data-{{ item_type }}-id="{{ item.id }}">
    <i class="bi bi-arrow-up"></i>
  </button>
  <span class="vote-score" data-{{ item_type }}-id="{{ item.id }}">{{ item.vote_score }}</span>
  <button class="vote-btn downvote {{ 'active' if user_vote and user_vote.vote_type == 'downvote' else '' }}"
          data-vote-type="downvote" data-{{ item_type }}-id="{{ item.id }}">
    <i class="bi bi-arrow-down"></i>
  </button>
</div>
{% endmacro %}

{% block content %}
<div class="discussion-board">
  <div class="board-header mb-4">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <div class="board-title-section">
        <h2>Discussion Board</h2>
        <p class="lead mb-0">Share ideas, ask questions, and connect with fellow members</p>
      </div>
      <div class="board-controls d-flex gap-3 align-items-center">
        <!-- Create Post Button -->
        <a class="btn btn-primary" href="{{ url_for('dash.new_post') }}{% if active_board %}?board={{ active_board }}{% endif %}">
          <i class="bi bi-plus-circle me-2"></i>Create Post
        </a>

        <!-- Sort Controls -->
        <div class="sort-controls">
          <div class="btn-group" role="group" aria-label="Sort options">
            <a href="{{ url_for('dash.dashboard', sort='hot') }}"
               class="btn btn-sm {{ 'btn-primary' if sort_by == 'hot' else 'btn-outline-primary' }}">
              <i class="bi bi-fire me-1"></i>Hot
            </a>
            <a href="{{ url_for('dash.dashboard', sort='new') }}"
               class="btn btn-sm {{ 'btn-primary' if sort_by == 'new' else 'btn-outline-primary' }}">
              <i class="bi bi-clock me-1"></i>New
            </a>
            <a href="{{ url_for('dash.dashboard', sort='top') }}"
               class="btn btn-sm {{ 'btn-primary' if sort_by == 'top' else 'btn-outline-primary' }}">
              <i class="bi bi-star me-1"></i>Top
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Boards Section -->
  {% if boards %}
  <div class="boards-section mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h3 class="mb-0">Boards</h3>
      <div class="d-flex align-items-center gap-2">
        {% if active_board %}
          <span class="badge bg-primary">Viewing: {{ active_board }}</span>
          <a href="{{ url_for('dash.dashboard') }}" class="btn btn-sm btn-outline-secondary">Clear filter</a>
        {% endif %}
        <a href="{{ url_for('dash.request_board') }}" class="btn btn-sm btn-outline-primary">
          <i class="bi bi-plus-circle me-1"></i>Apply to create a board
        </a>
      </div>
    </div>

    <div class="row g-3">
      {% for b in boards %}
      <div class="col-md-4 col-lg-3">
        <div class="card board-card h-100">
          <div class="card-body d-flex flex-column">
            <h5 class="card-title mb-1">{{ b.name }}</h5>
            <p class="card-text text-light flex-grow-1">{{ b.description or 'No description' }}</p>
            <div class="d-flex gap-2">
              <a class="btn btn-sm btn-outline-secondary" href="{{ url_for('dash.dashboard') }}?board={{ b.name }}">View</a>
              <a class="btn btn-sm btn-primary" href="{{ url_for('dash.new_post') }}?board={{ b.name }}">Create Post</a>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}


  <!-- Create Post Modal -->
  <div class="modal fade" id="createPostModal" tabindex="-1" aria-labelledby="createPostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createPostModalLabel">
            <i class="bi bi-plus-circle me-2"></i>Create New Post
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="POST" action="{{ url_for('dash.create_post') }}" class="needs-validation" novalidate id="createPostForm">
          <div class="modal-body">
            <input type="hidden" name="board" value="General">
            <input type="hidden" name="content" id="hiddenContent">

            <div class="form-group mb-3">
              <label for="postTitle" class="form-label required">Post Title</label>
              <input type="text" class="form-control" id="postTitle" name="title"
                     placeholder="What would you like to discuss?" required maxlength="200">
              <div class="invalid-feedback">
                Please provide a title for your post.
              </div>
              <div class="form-text">Keep it clear and descriptive (max 200 characters).</div>
            </div>

            <div class="form-group mb-3">
              <label for="postContent" class="form-label required">Post Content</label>
              <div id="postContentEditor" class="rich-text-editor"></div>
              <div class="invalid-feedback" id="contentFeedback">
                Please write some content for your post.
              </div>
              <div class="form-text">Use the toolbar to format your content. Be respectful and constructive.</div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-circle me-2"></i>Cancel
            </button>
            <button type="submit" class="btn btn-primary" id="submitPostBtn">
              <i class="bi bi-send me-2"></i>Create Post
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Posts List -->
  <div class="posts-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3>Discussions</h3>
      <small class="text-light">{{ posts|length }} post{{ 's' if posts|length != 1 else '' }}</small>
    </div>

    {% if posts %}
      <div class="posts-list">
        {% for post in posts %}
        <article class="reddit-post" data-post-id="{{ post.id }}">
          <div class="post-vote-section">
            {{ render_vote_buttons(post, 'post') }}
          </div>

          <div class="post-content-section">
            <div class="post-header">
              <div class="post-meta">
                <span class="post-board">v/{{ post.board }}</span>
                <span class="post-separator">•</span>
                <span class="post-author">
                  Posted by
                  <a href="#" class="username">u/{{ post.author.username }}</a>
                  {% if post.author_id == post.author_id %}<span class="op-badge">OP</span>{% endif %}
                </span>
                <span class="post-separator">•</span>
                <span class="post-time" title="{{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}">
                  {{ time_ago(post.created_at) }}
                </span>
              </div>
            </div>

            <div class="post-title">
              <h4><a href="{{ url_for('dash.view_post', post_id=post.id) }}" class="post-link">{{ post.title }}</a></h4>
            </div>

            <div class="post-body">
              <div class="post-text">
                {{ post.content[:300] }}{% if post.content|length > 300 %}...{% endif %}
              </div>
            </div>

            <div class="post-footer">
              <div class="post-actions">
                <button class="action-btn comment-btn" onclick="toggleCommentForm({{ post.id }})">
                  <i class="bi bi-chat"></i>
                  <span>{{ post.comment_count }} comment{{ 's' if post.comment_count != 1 else '' }}</span>
                </button>

                <button class="action-btn share-btn">
                  <i class="bi bi-share"></i>
                  <span>Share</span>
                </button>

                <button class="action-btn save-btn">
                  <i class="bi bi-bookmark"></i>
                  <span>Save</span>
                </button>
              </div>
            </div>

            <!-- Quick Comment Form -->
            <div id="comment-form-{{ post.id }}" class="quick-comment-form" style="display: none;">
              <form method="POST" action="{{ url_for('dash.create_comment') }}" class="comment-form">
                <input type="hidden" name="post_id" value="{{ post.id }}">
                <div class="comment-input-wrapper">
                  <textarea name="content" class="comment-input" placeholder="What are your thoughts?" rows="3" required></textarea>
                </div>
                <div class="comment-actions">
                  <button type="button" class="btn btn-sm btn-secondary" onclick="toggleCommentForm({{ post.id }})">
                    Cancel
                  </button>
                  <button type="submit" class="btn btn-sm btn-primary">
                    Comment
                  </button>
                </div>
              </form>
            </div>
          </div>
        </article>
        {% endfor %}
      </div>
    {% else %}
      <div class="empty-state text-center py-5">
        <div class="empty-icon mb-3">
          <i class="bi bi-chat-dots" style="font-size: 3rem; opacity: 0.3;"></i>
        </div>
        <h4>No posts yet</h4>
        <p class="text-light">Be the first to start a discussion in the community!</p>
        <a class="btn btn-primary" href="{{ url_for('dash.new_post') }}{% if active_board %}?board={{ active_board }}{% endif %}">
          <i class="bi bi-plus-circle me-2"></i>Create First Post
        </a>
      </div>
    {% endif %}
  </div>
</div>

<script>
// Rich Text Editor and Form Handling
// Remove duplicate declaration

// Initialize Quill editor - single initialization
let postContentQuill = null;
let quillInitialized = false;

function initializeQuillEditor() {
  if (!quillInitialized && document.getElementById('postContentEditor')) {
    try {
      postContentQuill = new Quill('#postContentEditor', {
        theme: 'snow',
        placeholder: 'Share your thoughts, questions, or insights...',
        modules: {
          toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['blockquote', 'code-block'],
            ['link'],
            ['clean']
          ]
        }
      });

      // Set minimum height for the editor
      const editorContainer = document.querySelector('#postContentEditor .ql-container');
      if (editorContainer) {
        editorContainer.style.minHeight = '150px';
      }

      quillInitialized = true;
      console.log('Quill editor initialized successfully');
    } catch (error) {
      console.error('Error initializing Quill editor:', error);
    }
  }
}

document.addEventListener('DOMContentLoaded', function() {
  const createPostModal = document.getElementById('createPostModal');

  if (createPostModal) {
    // Initialize when modal is shown
    createPostModal.addEventListener('shown.bs.modal', function() {
      console.log('Modal shown, initializing Quill editor...');
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        initializeQuillEditor();
        // Focus on title input first
        const titleInput = document.getElementById('postTitle');
        if (titleInput) {
          titleInput.focus();
        }
      }, 150);
    });

    // Clean up when modal is hidden
    createPostModal.addEventListener('hidden.bs.modal', function() {
      console.log('Modal hidden, cleaning up...');
      // Reset form and validation states
      const form = this.querySelector('form');
      if (form) {
        form.reset();
        form.classList.remove('was-validated');

        // Remove validation classes
        form.querySelectorAll('.is-valid, .is-invalid').forEach(element => {
          element.classList.remove('is-valid', 'is-invalid');
        });
      }

      // Call additional reset function
      resetModalForm();
    });
  }
});

// Enhanced form validation with Quill support
(function() {
  'use strict';

  const form = document.getElementById('createPostForm');

  if (form) {
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      event.stopPropagation();

      let isValid = true;

      // Validate title
      const titleInput = document.getElementById('postTitle');
      if (!titleInput.value.trim()) {
        titleInput.classList.add('is-invalid');
        isValid = false;
      } else {
        titleInput.classList.remove('is-invalid');
        titleInput.classList.add('is-valid');
      }

      // Validate Quill content
      const contentFeedback = document.getElementById('contentFeedback');
      let quillContent = '';
      let quillHtml = '';

      if (postContentQuill && quillInitialized) {
        quillContent = postContentQuill.getText().trim();
        quillHtml = postContentQuill.root.innerHTML;
      }

      if (quillContent.length < 10) {
        contentFeedback.style.display = 'block';
        contentFeedback.textContent = 'Please write at least 10 characters for your post content.';
        const editorElement = document.getElementById('postContentEditor');
        if (editorElement) {
          editorElement.style.border = '2px solid var(--color-error)';
          editorElement.style.borderRadius = 'var(--radius-md)';
        }
        isValid = false;
      } else {
        contentFeedback.style.display = 'none';
        const editorElement = document.getElementById('postContentEditor');
        if (editorElement) {
          editorElement.style.border = '2px solid var(--color-success)';
          editorElement.style.borderRadius = 'var(--radius-md)';
        }

        // Set the hidden content field with HTML from Quill
        document.getElementById('hiddenContent').value = quillHtml;
      }

      if (isValid) {
        // Show loading state
        const submitBtn = document.getElementById('submitPostBtn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
        submitBtn.disabled = true;

        // Submit the form
        form.submit();
      } else {
        form.classList.add('was-validated');
      }
    });

    // Real-time validation feedback for title
    const titleInput = document.getElementById('postTitle');
    if (titleInput) {
      titleInput.addEventListener('blur', function() {
        if (this.value.trim()) {
          this.classList.remove('is-invalid');
          this.classList.add('is-valid');
        } else {
          this.classList.remove('is-valid');
          this.classList.add('is-invalid');
        }
      });
    }
  }
})();

// Voting functionality
function handleVote(event) {
  event.preventDefault();

  const button = event.currentTarget;
  const voteType = button.dataset.voteType;
  const postId = button.dataset.postId;
  const commentId = button.dataset.commentId;

  // Show loading state
  button.classList.add('loading');

  const data = {
    vote_type: voteType
  };

  if (postId) {
    data.post_id = parseInt(postId);
  } else if (commentId) {
    data.comment_id = parseInt(commentId);
  }

  fetch('{{ url_for("dash.vote") }}', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Update vote score
      const targetId = postId || commentId;
      const targetType = postId ? 'post' : 'comment';
      const scoreElement = document.querySelector(`[data-${targetType}-id="${targetId}"] .vote-score`);
      if (scoreElement) {
        scoreElement.textContent = data.vote_score;
      }

      // Update button states
      const voteContainer = button.closest('.vote-buttons');
      const upvoteBtn = voteContainer.querySelector('.upvote');
      const downvoteBtn = voteContainer.querySelector('.downvote');

      // Remove all active states
      upvoteBtn.classList.remove('active');
      downvoteBtn.classList.remove('active');

      // Add active state based on current vote
      if (data.action !== 'removed') {
        if (data.vote_type === 'upvote') {
          upvoteBtn.classList.add('active');
        } else if (data.vote_type === 'downvote') {
          downvoteBtn.classList.add('active');
        }
      }
    } else {
      console.error('Vote failed:', data.error);
      showPopup('Failed to vote. Please try again.', 'error');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    showPopup('Network error. Please try again.', 'error');
  })
  .finally(() => {
    button.classList.remove('loading');
  });
}

// Toggle comment forms
function toggleCommentForm(postId) {
  const commentForm = document.getElementById(`comment-form-${postId}`);
  if (commentForm.style.display === 'none' || !commentForm.style.display) {
    commentForm.style.display = 'block';
    commentForm.querySelector('textarea').focus();
  } else {
    commentForm.style.display = 'none';
  }
}

// Auto-expand textareas
document.querySelectorAll('textarea').forEach(textarea => {
  textarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
  });
});

// Attach vote event listeners
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.vote-btn').forEach(button => {
    button.addEventListener('click', handleVote);
  });
});

// Simple notification system
function showNotification(message, type = 'info') {
  // Delegate to global popup for consistent UX
  if (window.showPopup) {
    window.showPopup(message, type);
  } else {
    // Fallback to native alert if needed
    alert(message);
  }
}

// Additional modal reset functionality (integrated above)
function resetModalForm() {
  const form = document.getElementById('createPostForm');
  if (form) {
    // Reset submit button
    const submitBtn = document.getElementById('submitPostBtn');
    if (submitBtn) {
      submitBtn.innerHTML = '<i class="bi bi-send me-2"></i>Create Post';
      submitBtn.disabled = false;
    }

    // Reset Quill editor
    if (postContentQuill && quillInitialized) {
      try {
        postContentQuill.setContents([]);
      } catch (error) {
        console.error('Error resetting Quill editor:', error);
      }
    }


    // Reset content feedback
    const contentFeedback = document.getElementById('contentFeedback');
    if (contentFeedback) {
      contentFeedback.style.display = 'none';
    }

    // Reset editor border
    const editor = document.getElementById('postContentEditor');
    if (editor) {
      editor.style.border = '';
    }
  }
}

// Error handling for modal operations
function handleModalError(error, operation) {
  console.error(`Modal ${operation} error:`, error);
  showNotification(`An error occurred while ${operation}. Please try again.`, 'error');
}

// Improved modal state management
function ensureModalState() {
  const modal = document.getElementById('createPostModal');
  if (modal) {
    // Ensure modal is properly initialized
    if (!modal.hasAttribute('data-bs-backdrop')) {
      modal.setAttribute('data-bs-backdrop', 'static');
      modal.setAttribute('data-bs-keyboard', 'false');
    }
  }
}
</script>

<style>
/* Enhanced Discussion Board Styles */
.reddit-post {
  display: flex;
  background: linear-gradient(145deg, var(--color-bg-tertiary) 0%, var(--color-bg-elevated) 100%);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  transition: all var(--transition-normal) ease-out;
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(8px);
}

.reddit-post::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-primary-alpha), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.reddit-post:hover {
  border-color: var(--color-border-light);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.reddit-post:hover::before {
  opacity: 1;
}

.post-vote-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--color-bg-quaternary);
  border-right: 1px solid var(--color-border);
  min-width: 60px;
}

.vote-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.vote-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-subtle);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast) ease-out;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.vote-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--color-primary-alpha);
  border-radius: 50%;
  transition: all var(--transition-fast);
  transform: translate(-50%, -50%);
}

.vote-btn:hover {
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  border-color: var(--color-border);
  transform: scale(1.1);
}

.vote-btn:hover::before {
  width: 100%;
  height: 100%;
}

.vote-btn.upvote.active {
  color: #ff6b35;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.15), rgba(255, 107, 53, 0.05));
  border-color: rgba(255, 107, 53, 0.3);
  box-shadow: 0 0 12px rgba(255, 107, 53, 0.2);
}

.vote-btn.downvote.active {
  color: #4a90e2;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.15), rgba(74, 144, 226, 0.05));
  border-color: rgba(74, 144, 226, 0.3);
  box-shadow: 0 0 12px rgba(74, 144, 226, 0.2);
}

.vote-btn.loading {
  opacity: 0.5;
  pointer-events: none;
}

.vote-score {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  min-width: 20px;
  text-align: center;
}

.post-content-section {
  flex: 1;
  padding: var(--spacing-md);
}

.post-header {
  margin-bottom: var(--spacing-sm);
}

.post-meta {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.post-board {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.post-separator {
  opacity: 0.5;
}

.username {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.username:hover {
  color: var(--color-primary);
  text-decoration: underline;
}

.op-badge {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
}

.post-title h4 {
  margin: 0;
  font-size: var(--font-size-lg);
  line-height: var(--line-height-tight);
}

.post-link {
  color: var(--color-text-white);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.post-link:hover {
  color: var(--color-primary);
}

.post-body {
  margin: var(--spacing-sm) 0;
}

.post-text {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.post-footer {
  margin-top: var(--spacing-md);
}

.post-actions {
  display: flex;
  gap: var(--spacing-md);
}

.action-btn {
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.quick-comment-form {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.comment-input-wrapper {
  margin-bottom: var(--spacing-sm);
}

.comment-input {
  width: 100%;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--color-text-primary);
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.comment-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.comment-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.board-controls {
  flex-wrap: wrap;
  gap: var(--spacing-md) !important;
}

.sort-controls .btn-group .btn {
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  white-space: nowrap;
}

.board-title-section {
  min-width: 0;
  flex: 1;
}

.empty-state {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-3xl);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .reddit-post {
    flex-direction: column;
  }

  .post-vote-section {
    flex-direction: row;
    justify-content: center;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
    min-width: auto;
    padding: var(--spacing-sm);
  }

  .vote-buttons {
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .post-actions {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .action-btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs);
  }

  .board-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: var(--spacing-md);
  }

  .board-controls {
    width: 100%;
    justify-content: space-between;
  }

  .sort-controls .btn-group {
    flex-wrap: wrap;
  }

  .board-title-section h2 {
    font-size: var(--font-size-xl);
  }

  .board-title-section .lead {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 576px) {
  .post-content-section {
    padding: var(--spacing-sm);
  }

  .post-meta {
    flex-wrap: wrap;
  }

  .comment-actions {
    justify-content: flex-start;
  }
}

/* Board cards */
.boards-section { margin-bottom: var(--spacing-lg); }
.board-card { background: var(--color-bg-tertiary); border: 1px solid var(--color-border); }
.board-card .card-title { color: var(--color-text-white); }
.board-card .btn { padding: 4px 10px; }

</style>
{% endblock %}
