{% extends "layout.html" %}

{% macro render_vote_buttons(item, item_type) %}
<div class="vote-buttons" data-{{ item_type }}-id="{{ item.id }}" role="group" aria-label="Vote on this {{ item_type }}">
  {% set user_vote = item.get_user_vote(current_user.id) if current_user.is_authenticated else None %}
  <button class="vote-btn upvote focus-visible {{ 'active' if user_vote and user_vote.vote_type == 'upvote' else '' }}"
          data-vote-type="upvote" data-{{ item_type }}-id="{{ item.id }}"
          aria-label="Upvote this {{ item_type }}"
          aria-pressed="{{ 'true' if user_vote and user_vote.vote_type == 'upvote' else 'false' }}"
          title="Upvote">
    <i class="bi bi-arrow-up" aria-hidden="true"></i>
  </button>
  <span class="vote-score" data-{{ item_type }}-id="{{ item.id }}" aria-label="Current score: {{ item.vote_score }}" role="status">{{ item.vote_score }}</span>
  <button class="vote-btn downvote focus-visible {{ 'active' if user_vote and user_vote.vote_type == 'downvote' else '' }}"
          data-vote-type="downvote" data-{{ item_type }}-id="{{ item.id }}"
          aria-label="Downvote this {{ item_type }}"
          aria-pressed="{{ 'true' if user_vote and user_vote.vote_type == 'downvote' else 'false' }}"
          title="Downvote">
    <i class="bi bi-arrow-down" aria-hidden="true"></i>
  </button>
</div>
{% endmacro %}

{% block content %}
<div class="modern-dashboard">
  <!-- Hero Header Section -->
  <div class="dashboard-hero">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">Community Hub</h1>
        <p class="hero-subtitle">Connect, share, and engage with fellow members</p>
      </div>
      <div class="hero-actions">
        <a class="btn btn-primary btn-lg hero-cta" href="{{ url_for('dash.new_post') }}{% if active_board %}?board={{ active_board }}{% endif %}">
          <i class="bi bi-plus-circle me-2"></i>Start Discussion
        </a>
      </div>
    </div>

    <!-- Quick Stats Bar -->
    <div class="stats-bar">
      <div class="stat-item">
        <div class="stat-value">{{ posts|length }}</div>
        <div class="stat-label">Active Discussions</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ boards|length }}</div>
        <div class="stat-label">Community Boards</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ current_user.username }}</div>
        <div class="stat-label">Welcome back</div>
      </div>
    </div>
  </div>

  <!-- Navigation & Filters -->
  <div class="dashboard-nav">
    <div class="nav-content">
      <div class="nav-section">
        <h3 class="nav-title">Browse Discussions</h3>
        <div class="sort-controls">
          <div class="btn-group" role="group" aria-label="Sort options">
            <a href="{{ url_for('dash.dashboard', sort='hot') }}"
               class="btn btn-sort {{ 'active' if sort_by == 'hot' else '' }}">
              <i class="bi bi-fire me-1"></i>Hot
            </a>
            <a href="{{ url_for('dash.dashboard', sort='new') }}"
               class="btn btn-sort {{ 'active' if sort_by == 'new' else '' }}">
              <i class="bi bi-clock me-1"></i>New
            </a>
            <a href="{{ url_for('dash.dashboard', sort='top') }}"
               class="btn btn-sort {{ 'active' if sort_by == 'top' else '' }}">
              <i class="bi bi-star me-1"></i>Top
            </a>
          </div>
        </div>
      </div>

      {% if active_board %}
      <div class="active-filter">
        <span class="filter-label">Viewing:</span>
        <span class="filter-value">{{ active_board }}</span>
        <a href="{{ url_for('dash.dashboard') }}" class="filter-clear" title="Clear filter">
          <i class="bi bi-x-circle"></i>
        </a>
      </div>
      {% endif %}
    </div>
  </div>
  <!-- Community Boards Section -->
  {% if boards %}
  <div class="boards-showcase">
    <div class="section-header">
      <div class="section-title-group">
        <h2 class="section-title">Community Boards</h2>
        <p class="section-subtitle">Explore specialized discussion areas</p>
      </div>
      <div class="section-actions">
        <a href="{{ url_for('dash.request_board') }}" class="btn btn-outline-primary">
          <i class="bi bi-plus-circle me-2"></i>Request New Board
        </a>
      </div>
    </div>

    <div class="boards-grid">
      {% for b in boards %}
      <article class="board-card-modern slide-up" data-board="{{ b.name }}" role="article" aria-labelledby="board-name-{{ loop.index }}">
        <div class="board-card-inner">
          <header class="board-header">
            <div class="board-icon" aria-hidden="true">
              <i class="bi bi-chat-square-text"></i>
            </div>
            <div class="board-meta">
              <h3 class="board-name" id="board-name-{{ loop.index }}">{{ b.name }}</h3>
              <p class="board-description">{{ b.description or 'Join the conversation in this community space' }}</p>
            </div>
          </header>

          <div class="board-stats" role="group" aria-label="Board statistics">
            <div class="stat">
              <span class="stat-number" aria-label="{{ b.posts|length if b.posts else 0 }} posts">{{ b.posts|length if b.posts else 0 }}</span>
              <span class="stat-text">Posts</span>
            </div>
            <div class="stat">
              <span class="stat-number" aria-label="{{ b.members|length if b.members else 0 }} members">{{ b.members|length if b.members else 0 }}</span>
              <span class="stat-text">Members</span>
            </div>
          </div>

          <div class="board-actions" role="group" aria-label="Board actions">
            <a class="btn-board-action primary focus-visible" href="{{ url_for('dash.dashboard') }}?board={{ b.name }}" aria-label="Browse {{ b.name }} board">
              <i class="bi bi-eye me-1" aria-hidden="true"></i>Browse
            </a>
            <a class="btn-board-action secondary focus-visible" href="{{ url_for('dash.new_post') }}?board={{ b.name }}" aria-label="Create new post in {{ b.name }}">
              <i class="bi bi-plus me-1" aria-hidden="true"></i>Post
            </a>
          </div>
        </div>

        <div class="board-card-glow" aria-hidden="true"></div>
      </article>
      {% endfor %}
    </div>
  </div>
  {% endif %}


  <!-- Create Post Modal -->
  <div class="modal fade" id="createPostModal" tabindex="-1" aria-labelledby="createPostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createPostModalLabel">
            <i class="bi bi-plus-circle me-2"></i>Create New Post
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="POST" action="{{ url_for('dash.create_post') }}" class="needs-validation" novalidate id="createPostForm">
          <div class="modal-body">
            <input type="hidden" name="board" value="General">
            <input type="hidden" name="content" id="hiddenContent">

            <div class="form-group mb-3">
              <label for="postTitle" class="form-label required">Post Title</label>
              <input type="text" class="form-control" id="postTitle" name="title"
                     placeholder="What would you like to discuss?" required maxlength="200">
              <div class="invalid-feedback">
                Please provide a title for your post.
              </div>
              <div class="form-text">Keep it clear and descriptive (max 200 characters).</div>
            </div>

            <div class="form-group mb-3">
              <label for="postContent" class="form-label required">Post Content</label>
              <div id="postContentEditor" class="rich-text-editor"></div>
              <div class="invalid-feedback" id="contentFeedback">
                Please write some content for your post.
              </div>
              <div class="form-text">Use the toolbar to format your content. Be respectful and constructive.</div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-circle me-2"></i>Cancel
            </button>
            <button type="submit" class="btn btn-primary" id="submitPostBtn">
              <i class="bi bi-send me-2"></i>Create Post
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Discussions Feed -->
  <div class="discussions-feed">
    <div class="feed-header">
      <div class="feed-title-group">
        <h2 class="feed-title">Latest Discussions</h2>
        <span class="feed-count">{{ posts|length }} active conversation{{ 's' if posts|length != 1 else '' }}</span>
      </div>
    </div>

    {% if posts %}
      <div class="posts-container">
        {% for post in posts %}
        <article class="modern-post fade-in" data-post-id="{{ post.id }}" role="article" aria-labelledby="post-title-{{ post.id }}">
          <div class="post-engagement" role="group" aria-label="Post voting">
            {{ render_vote_buttons(post, 'post') }}
          </div>

          <div class="post-main">
            <header class="post-header-modern">
              <div class="post-meta-modern">
                <span class="post-board-tag" role="tag" aria-label="Board: {{ post.board }}">{{ post.board }}</span>
                <span class="meta-separator" aria-hidden="true">•</span>
                <span class="post-author-info">
                  <a href="#" class="author-link" aria-label="View profile of {{ post.author.username }}">{{ post.author.username }}</a>
                  {% if post.author_id == post.author_id %}<span class="author-badge" title="Original Poster">OP</span>{% endif %}
                </span>
                <span class="meta-separator" aria-hidden="true">•</span>
                <time class="post-timestamp" datetime="{{ post.created_at.isoformat() }}" title="{{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}">
                  {{ time_ago(post.created_at) }}
                </time>
              </div>
            </header>

            <div class="post-content-modern">
              <h3 class="post-title-modern" id="post-title-{{ post.id }}">
                <a href="{{ url_for('dash.view_post', post_id=post.id) }}" class="post-title-link" aria-describedby="post-excerpt-{{ post.id }}">{{ post.title }}</a>
              </h3>

              <div class="post-preview">
                <div class="post-excerpt" id="post-excerpt-{{ post.id }}">
                  {{ post.content[:280] }}{% if post.content|length > 280 %}...{% endif %}
                </div>
              </div>
            </div>

            <footer class="post-interactions">
              <div class="interaction-buttons" role="group" aria-label="Post actions">
                <button class="interaction-btn comment-btn focus-visible" onclick="toggleCommentForm({{ post.id }})" aria-label="Comment on this post ({{ post.comment_count }} existing comments)" aria-expanded="false" aria-controls="comment-form-{{ post.id }}">
                  <i class="bi bi-chat-dots" aria-hidden="true"></i>
                  <span class="btn-text">{{ post.comment_count }}</span>
                  <span class="btn-label">Comment{{ 's' if post.comment_count != 1 else '' }}</span>
                </button>

                <button class="interaction-btn share-btn focus-visible" aria-label="Share this post">
                  <i class="bi bi-share" aria-hidden="true"></i>
                  <span class="btn-text">Share</span>
                </button>

                <button class="interaction-btn bookmark-btn focus-visible" aria-label="Bookmark this post">
                  <i class="bi bi-bookmark" aria-hidden="true"></i>
                  <span class="btn-text">Save</span>
                </button>

                <a href="{{ url_for('dash.view_post', post_id=post.id) }}" class="interaction-btn read-more-btn focus-visible" aria-label="Read full post: {{ post.title }}">
                  <i class="bi bi-arrow-right" aria-hidden="true"></i>
                  <span class="btn-text">Read More</span>
                </a>
              </div>
            </footer>

            <!-- Quick Comment Form -->
            <div id="comment-form-{{ post.id }}" class="modern-comment-form" style="display: none;">
              <form method="POST" action="{{ url_for('dash.create_comment') }}" class="comment-form-modern">
                <input type="hidden" name="post_id" value="{{ post.id }}">
                <div class="comment-input-container">
                  <textarea name="content" class="comment-textarea" placeholder="Share your thoughts..." rows="3" required></textarea>
                </div>
                <div class="comment-form-actions">
                  <button type="button" class="btn-comment-action cancel" onclick="toggleCommentForm({{ post.id }})">
                    Cancel
                  </button>
                  <button type="submit" class="btn-comment-action submit">
                    <i class="bi bi-send me-1"></i>Post Comment
                  </button>
                </div>
              </form>
            </div>
          </div>
        </article>
        {% endfor %}
      </div>
    {% else %}
      <div class="empty-state-modern">
        <div class="empty-illustration">
          <div class="empty-icon-container">
            <i class="bi bi-chat-square-text empty-icon"></i>
            <div class="empty-icon-glow"></div>
          </div>
        </div>
        <div class="empty-content">
          <h3 class="empty-title">Start the Conversation</h3>
          <p class="empty-description">Be the first to share your thoughts and spark engaging discussions in the community.</p>
          <a class="btn-empty-action" href="{{ url_for('dash.new_post') }}{% if active_board %}?board={{ active_board }}{% endif %}">
            <i class="bi bi-plus-circle me-2"></i>Create Your First Post
          </a>
        </div>
      </div>
    {% endif %}
  </div>
</div>

<script>
// Rich Text Editor and Form Handling
// Remove duplicate declaration

// Initialize Quill editor - single initialization
let postContentQuill = null;
let quillInitialized = false;

function initializeQuillEditor() {
  if (!quillInitialized && document.getElementById('postContentEditor')) {
    try {
      postContentQuill = new Quill('#postContentEditor', {
        theme: 'snow',
        placeholder: 'Share your thoughts, questions, or insights...',
        modules: {
          toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['blockquote', 'code-block'],
            ['link'],
            ['clean']
          ]
        }
      });

      // Set minimum height for the editor
      const editorContainer = document.querySelector('#postContentEditor .ql-container');
      if (editorContainer) {
        editorContainer.style.minHeight = '150px';
      }

      quillInitialized = true;
      console.log('Quill editor initialized successfully');
    } catch (error) {
      console.error('Error initializing Quill editor:', error);
    }
  }
}

document.addEventListener('DOMContentLoaded', function() {
  const createPostModal = document.getElementById('createPostModal');

  if (createPostModal) {
    // Initialize when modal is shown
    createPostModal.addEventListener('shown.bs.modal', function() {
      console.log('Modal shown, initializing Quill editor...');
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        initializeQuillEditor();
        // Focus on title input first
        const titleInput = document.getElementById('postTitle');
        if (titleInput) {
          titleInput.focus();
        }
      }, 150);
    });

    // Clean up when modal is hidden
    createPostModal.addEventListener('hidden.bs.modal', function() {
      console.log('Modal hidden, cleaning up...');
      // Reset form and validation states
      const form = this.querySelector('form');
      if (form) {
        form.reset();
        form.classList.remove('was-validated');

        // Remove validation classes
        form.querySelectorAll('.is-valid, .is-invalid').forEach(element => {
          element.classList.remove('is-valid', 'is-invalid');
        });
      }

      // Call additional reset function
      resetModalForm();
    });
  }
});

// Enhanced form validation with Quill support
(function() {
  'use strict';

  const form = document.getElementById('createPostForm');

  if (form) {
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      event.stopPropagation();

      let isValid = true;

      // Validate title
      const titleInput = document.getElementById('postTitle');
      if (!titleInput.value.trim()) {
        titleInput.classList.add('is-invalid');
        isValid = false;
      } else {
        titleInput.classList.remove('is-invalid');
        titleInput.classList.add('is-valid');
      }

      // Validate Quill content
      const contentFeedback = document.getElementById('contentFeedback');
      let quillContent = '';
      let quillHtml = '';

      if (postContentQuill && quillInitialized) {
        quillContent = postContentQuill.getText().trim();
        quillHtml = postContentQuill.root.innerHTML;
      }

      if (quillContent.length < 10) {
        contentFeedback.style.display = 'block';
        contentFeedback.textContent = 'Please write at least 10 characters for your post content.';
        const editorElement = document.getElementById('postContentEditor');
        if (editorElement) {
          editorElement.style.border = '2px solid var(--color-error)';
          editorElement.style.borderRadius = 'var(--radius-md)';
        }
        isValid = false;
      } else {
        contentFeedback.style.display = 'none';
        const editorElement = document.getElementById('postContentEditor');
        if (editorElement) {
          editorElement.style.border = '2px solid var(--color-success)';
          editorElement.style.borderRadius = 'var(--radius-md)';
        }

        // Set the hidden content field with HTML from Quill
        document.getElementById('hiddenContent').value = quillHtml;
      }

      if (isValid) {
        // Show loading state
        const submitBtn = document.getElementById('submitPostBtn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
        submitBtn.disabled = true;

        // Submit the form
        form.submit();
      } else {
        form.classList.add('was-validated');
      }
    });

    // Real-time validation feedback for title
    const titleInput = document.getElementById('postTitle');
    if (titleInput) {
      titleInput.addEventListener('blur', function() {
        if (this.value.trim()) {
          this.classList.remove('is-invalid');
          this.classList.add('is-valid');
        } else {
          this.classList.remove('is-valid');
          this.classList.add('is-invalid');
        }
      });
    }
  }
})();

// Voting functionality
function handleVote(event) {
  event.preventDefault();

  const button = event.currentTarget;
  const voteType = button.dataset.voteType;
  const postId = button.dataset.postId;
  const commentId = button.dataset.commentId;

  // Show loading state
  button.classList.add('loading');

  const data = {
    vote_type: voteType
  };

  if (postId) {
    data.post_id = parseInt(postId);
  } else if (commentId) {
    data.comment_id = parseInt(commentId);
  }

  fetch('{{ url_for("dash.vote") }}', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Update vote score
      const targetId = postId || commentId;
      const targetType = postId ? 'post' : 'comment';
      const scoreElement = document.querySelector(`[data-${targetType}-id="${targetId}"] .vote-score`);
      if (scoreElement) {
        scoreElement.textContent = data.vote_score;
      }

      // Update button states
      const voteContainer = button.closest('.vote-buttons');
      const upvoteBtn = voteContainer.querySelector('.upvote');
      const downvoteBtn = voteContainer.querySelector('.downvote');

      // Remove all active states
      upvoteBtn.classList.remove('active');
      downvoteBtn.classList.remove('active');

      // Add active state based on current vote
      if (data.action !== 'removed') {
        if (data.vote_type === 'upvote') {
          upvoteBtn.classList.add('active');
        } else if (data.vote_type === 'downvote') {
          downvoteBtn.classList.add('active');
        }
      }
    } else {
      console.error('Vote failed:', data.error);
      showPopup('Failed to vote. Please try again.', 'error');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    showPopup('Network error. Please try again.', 'error');
  })
  .finally(() => {
    button.classList.remove('loading');
  });
}

// Toggle comment forms
function toggleCommentForm(postId) {
  const commentForm = document.getElementById(`comment-form-${postId}`);
  if (commentForm.style.display === 'none' || !commentForm.style.display) {
    commentForm.style.display = 'block';
    commentForm.querySelector('textarea').focus();
  } else {
    commentForm.style.display = 'none';
  }
}

// Auto-expand textareas
document.querySelectorAll('textarea').forEach(textarea => {
  textarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
  });
});

// Attach vote event listeners
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.vote-btn').forEach(button => {
    button.addEventListener('click', handleVote);
  });
});

// Simple notification system
function showNotification(message, type = 'info') {
  // Delegate to global popup for consistent UX
  if (window.showPopup) {
    window.showPopup(message, type);
  } else {
    // Fallback to native alert if needed
    alert(message);
  }
}

// Additional modal reset functionality (integrated above)
function resetModalForm() {
  const form = document.getElementById('createPostForm');
  if (form) {
    // Reset submit button
    const submitBtn = document.getElementById('submitPostBtn');
    if (submitBtn) {
      submitBtn.innerHTML = '<i class="bi bi-send me-2"></i>Create Post';
      submitBtn.disabled = false;
    }

    // Reset Quill editor
    if (postContentQuill && quillInitialized) {
      try {
        postContentQuill.setContents([]);
      } catch (error) {
        console.error('Error resetting Quill editor:', error);
      }
    }


    // Reset content feedback
    const contentFeedback = document.getElementById('contentFeedback');
    if (contentFeedback) {
      contentFeedback.style.display = 'none';
    }

    // Reset editor border
    const editor = document.getElementById('postContentEditor');
    if (editor) {
      editor.style.border = '';
    }
  }
}

// Error handling for modal operations
function handleModalError(error, operation) {
  console.error(`Modal ${operation} error:`, error);
  showNotification(`An error occurred while ${operation}. Please try again.`, 'error');
}

// Improved modal state management
function ensureModalState() {
  const modal = document.getElementById('createPostModal');
  if (modal) {
    // Ensure modal is properly initialized
    if (!modal.hasAttribute('data-bs-backdrop')) {
      modal.setAttribute('data-bs-backdrop', 'static');
      modal.setAttribute('data-bs-keyboard', 'false');
    }
  }
}
</script>

<style>
/* ===== MODERN DASHBOARD STYLES ===== */

/* Dashboard Hero Section */
.modern-dashboard {
  min-height: 100vh;
  background: var(--color-bg-primary);
}

.dashboard-hero {
  background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-tertiary) 50%, var(--color-bg-quaternary) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-3xl) var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
}

.dashboard-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, var(--color-primary-alpha-subtle) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  margin: 0 0 var(--spacing-sm) 0;
  background: linear-gradient(135deg, var(--color-text-white) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: var(--font-weight-normal);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
}

.hero-cta {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal) ease-out;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.hero-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow) ease-out;
}

.hero-cta:hover::before {
  left: 100%;
}

.hero-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-luxury);
}

/* Stats Bar */
.stats-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xl);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border-subtle);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-medium);
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, var(--color-border), transparent);
}

/* Dashboard Navigation */
.dashboard-nav {
  background: var(--color-bg-elevated);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(10px);
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.nav-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-white);
  margin: 0;
}

.sort-controls .btn-group {
  display: flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-sort {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast) ease-out;
  position: relative;
  overflow: hidden;
}

.btn-sort::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--color-primary-alpha-light), transparent);
  transition: left var(--transition-normal) ease-out;
}

.btn-sort:hover::before {
  left: 100%;
}

.btn-sort:hover {
  background: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  border-color: var(--color-border-light);
  transform: translateY(-1px);
}

.btn-sort.active {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  border-color: var(--color-primary);
  box-shadow: 0 0 20px var(--color-primary-alpha);
}

.active-filter {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--color-primary-alpha-light);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-primary-alpha);
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

.filter-value {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.filter-clear {
  color: var(--color-text-tertiary);
  transition: color var(--transition-fast);
  text-decoration: none;
}

.filter-clear:hover {
  color: var(--color-primary);
}

/* Boards Showcase */
.boards-showcase {
  margin-bottom: var(--spacing-3xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.section-title-group {
  flex: 1;
}

.section-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  margin: 0 0 var(--spacing-xs) 0;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-md);
}

.boards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
}

.board-card-modern {
  position: relative;
  background: linear-gradient(145deg, var(--color-bg-tertiary) 0%, var(--color-bg-elevated) 50%, var(--color-bg-quaternary) 100%);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  transition: all var(--transition-normal) ease-out;
  cursor: pointer;
  overflow: hidden;
  backdrop-filter: blur(15px);
}

.board-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-alpha-subtle) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.board-card-modern:hover::before {
  opacity: 1;
}

.board-card-modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-luxury);
  border-color: var(--color-primary-alpha);
}

.board-card-modern:hover .board-card-glow {
  opacity: 1;
  transform: scale(1.1);
}

.board-card-inner {
  position: relative;
  z-index: 2;
}

.board-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.board-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--color-bg-primary);
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.board-meta {
  flex: 1;
}

.board-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  margin: 0 0 var(--spacing-xs) 0;
}

.board-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.board-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--color-border-subtle);
  border-bottom: 1px solid var(--color-border-subtle);
}

.stat {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-medium);
}

.board-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-board-action {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-align: center;
  transition: all var(--transition-fast) ease-out;
  position: relative;
  overflow: hidden;
}

.btn-board-action.primary {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  border: 1px solid var(--color-primary);
}

.btn-board-action.secondary {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

.btn-board-action:hover {
  transform: translateY(-2px);
  text-decoration: none;
}

.btn-board-action.primary:hover {
  background: var(--color-primary-light);
  box-shadow: 0 8px 25px var(--color-primary-alpha);
  color: var(--color-bg-primary);
}

.btn-board-action.secondary:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-border-light);
  color: var(--color-text-primary);
}

.board-card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, var(--color-primary-alpha-light) 0%, transparent 70%);
  opacity: 0;
  transition: all var(--transition-slow) ease-out;
  pointer-events: none;
  z-index: 1;
}

/* Discussions Feed */
.discussions-feed {
  margin-bottom: var(--spacing-3xl);
}

.feed-header {
  margin-bottom: var(--spacing-xl);
}

.feed-title-group {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.feed-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  margin: 0;
}

.feed-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border-subtle);
}

.posts-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Modern Post Cards */
.modern-post {
  display: flex;
  background: linear-gradient(145deg, var(--color-bg-tertiary) 0%, var(--color-bg-elevated) 50%, var(--color-bg-quaternary) 100%);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  transition: all var(--transition-normal) ease-out;
  position: relative;
  backdrop-filter: blur(15px);
}

.modern-post::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--color-primary-alpha), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modern-post:hover::before {
  opacity: 1;
}

.modern-post:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-luxury);
  border-color: var(--color-border-light);
}

/* Post Engagement (Voting) */
.post-engagement {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.02);
  border-right: 1px solid var(--color-border-subtle);
  min-width: 80px;
  backdrop-filter: blur(10px);
}

.vote-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.vote-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-subtle);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast) ease-out;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.vote-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--color-primary-alpha);
  border-radius: 50%;
  transition: all var(--transition-fast);
  transform: translate(-50%, -50%);
}

.vote-btn:hover {
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  border-color: var(--color-border);
  transform: scale(1.05);
}

.vote-btn:hover::before {
  width: 100%;
  height: 100%;
}

.vote-btn.upvote.active {
  color: #ff6b35;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.2), rgba(255, 107, 53, 0.1));
  border-color: rgba(255, 107, 53, 0.4);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.vote-btn.downvote.active {
  color: #4a90e2;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(74, 144, 226, 0.1));
  border-color: rgba(74, 144, 226, 0.4);
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.vote-btn.loading {
  opacity: 0.5;
  pointer-events: none;
}

.vote-score {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  min-width: 24px;
  text-align: center;
  margin: var(--spacing-xs) 0;
}

/* Post Main Content */
.post-main {
  flex: 1;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.post-header-modern {
  margin-bottom: var(--spacing-sm);
}

.post-meta-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  flex-wrap: wrap;
}

.post-board-tag {
  background: var(--color-primary-alpha-light);
  color: var(--color-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid var(--color-primary-alpha);
}

.meta-separator {
  opacity: 0.5;
  font-weight: var(--font-weight-bold);
}

.author-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--transition-fast);
}

.author-link:hover {
  color: var(--color-primary);
  text-decoration: none;
}

.author-badge {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  font-size: 10px;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.post-timestamp {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

.post-content-modern {
  flex: 1;
}

.post-title-modern {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.post-title-link {
  color: var(--color-text-white);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.post-title-link:hover {
  color: var(--color-primary);
  text-decoration: none;
}

.post-preview {
  margin-bottom: var(--spacing-lg);
}

.post-excerpt {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
}

/* Post Interactions */
.post-interactions {
  border-top: 1px solid var(--color-border-subtle);
  padding-top: var(--spacing-md);
}

.interaction-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.interaction-btn {
  background: transparent;
  border: 1px solid var(--color-border-subtle);
  color: var(--color-text-tertiary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast) ease-out;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.interaction-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--color-primary-alpha-light), transparent);
  transition: left var(--transition-normal) ease-out;
}

.interaction-btn:hover::before {
  left: 100%;
}

.interaction-btn:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-border);
  color: var(--color-text-primary);
  transform: translateY(-2px);
  text-decoration: none;
}

.interaction-btn i {
  font-size: var(--font-size-base);
}

.btn-text {
  font-weight: var(--font-weight-semibold);
}

.btn-label {
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.comment-btn:hover {
  border-color: var(--color-info);
  color: var(--color-info);
}

.share-btn:hover {
  border-color: var(--color-success);
  color: var(--color-success);
}

.bookmark-btn:hover {
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.read-more-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* Modern Comment Form */
.modern-comment-form {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border-subtle);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.comment-form-modern {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.comment-input-container {
  position: relative;
}

.comment-textarea {
  width: 100%;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  resize: vertical;
  min-height: 100px;
  transition: all var(--transition-fast);
}

.comment-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-alpha);
  background: var(--color-bg-tertiary);
}

.comment-textarea::placeholder {
  color: var(--color-text-tertiary);
  font-style: italic;
}

.comment-form-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.btn-comment-action {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast) ease-out;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-comment-action.cancel {
  background: transparent;
  color: var(--color-text-secondary);
  border-color: var(--color-border);
}

.btn-comment-action.cancel:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.btn-comment-action.submit {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  border-color: var(--color-primary);
}

.btn-comment-action.submit:hover {
  background: var(--color-primary-light);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--color-primary-alpha);
}

/* Empty State */
.empty-state-modern {
  text-align: center;
  padding: var(--spacing-4xl) var(--spacing-xl);
  background: linear-gradient(145deg, var(--color-bg-tertiary) 0%, var(--color-bg-elevated) 100%);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--color-border);
  margin: var(--spacing-2xl) 0;
  position: relative;
  overflow: hidden;
}

.empty-state-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, var(--color-primary-alpha-subtle) 0%, transparent 70%);
  pointer-events: none;
}

.empty-illustration {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.empty-icon-container {
  position: relative;
  display: inline-block;
}

.empty-icon {
  font-size: 4rem;
  color: var(--color-text-tertiary);
  opacity: 0.6;
  position: relative;
  z-index: 2;
}

.empty-icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, var(--color-primary-alpha-light) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

.empty-content {
  position: relative;
  z-index: 2;
}

.empty-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  margin: 0 0 var(--spacing-md) 0;
}

.empty-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0 0 var(--spacing-xl) 0;
  line-height: var(--line-height-relaxed);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.btn-empty-action {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal) ease-out;
  border: 1px solid var(--color-primary);
  position: relative;
  overflow: hidden;
}

.btn-empty-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow) ease-out;
}

.btn-empty-action:hover::before {
  left: 100%;
}

.btn-empty-action:hover {
  background: var(--color-primary-light);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px var(--color-primary-alpha);
  text-decoration: none;
  color: var(--color-bg-primary);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet and smaller desktop screens */
@media (max-width: 1024px) {
  .hero-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }

  .hero-actions {
    width: 100%;
    justify-content: center;
  }

  .stats-bar {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .stat-divider {
    width: 100%;
    height: 1px;
  }

  .boards-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
  }

  .nav-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
}

/* Tablet screens */
@media (max-width: 768px) {
  .dashboard-hero {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .feed-title {
    font-size: var(--font-size-2xl);
  }

  .modern-post {
    flex-direction: column;
  }

  .post-engagement {
    flex-direction: row;
    justify-content: center;
    border-right: none;
    border-bottom: 1px solid var(--color-border-subtle);
    min-width: auto;
    padding: var(--spacing-md);
  }

  .vote-buttons {
    flex-direction: row;
    gap: var(--spacing-lg);
  }

  .interaction-buttons {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .interaction-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .boards-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .section-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Mobile screens */
@media (max-width: 576px) {
  .dashboard-hero {
    padding: var(--spacing-xl) var(--spacing-md);
    margin-bottom: var(--spacing-xl);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-sm);
  }

  .hero-cta {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }

  .stats-bar {
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .stat-value {
    font-size: var(--font-size-lg);
  }

  .dashboard-nav {
    padding: var(--spacing-lg);
  }

  .nav-title {
    font-size: var(--font-size-lg);
  }

  .sort-controls .btn-group {
    flex-wrap: wrap;
  }

  .btn-sort {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .section-title {
    font-size: var(--font-size-xl);
  }

  .feed-title {
    font-size: var(--font-size-xl);
  }

  .feed-title-group {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .board-card-modern {
    padding: var(--spacing-lg);
  }

  .board-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .board-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }

  .board-name {
    font-size: var(--font-size-lg);
  }

  .board-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .post-main {
    padding: var(--spacing-md);
  }

  .post-title-modern {
    font-size: var(--font-size-lg);
  }

  .post-meta-modern {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  .interaction-buttons {
    justify-content: space-between;
  }

  .interaction-btn {
    flex: 1;
    min-width: auto;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .btn-text {
    display: none;
  }

  .btn-label {
    display: none;
  }

  .comment-form-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .btn-comment-action {
    width: 100%;
    justify-content: center;
  }

  .empty-state-modern {
    padding: var(--spacing-2xl) var(--spacing-md);
  }

  .empty-title {
    font-size: var(--font-size-xl);
  }

  .empty-description {
    font-size: var(--font-size-base);
  }

  .btn-empty-action {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* Extra small screens */
@media (max-width: 375px) {
  .hero-title {
    font-size: var(--font-size-xl);
  }

  .section-title, .feed-title {
    font-size: var(--font-size-lg);
  }

  .post-title-modern {
    font-size: var(--font-size-base);
  }

  .interaction-btn i {
    font-size: var(--font-size-sm);
  }

  .vote-btn {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-base);
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .dashboard-hero {
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .stats-bar {
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .stat-divider {
    width: 1px;
    height: 30px;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .modern-post,
  .board-card-modern,
  .dashboard-hero,
  .dashboard-nav {
    border-width: 2px;
    border-color: var(--color-primary);
  }

  .vote-btn,
  .interaction-btn,
  .btn-sort {
    border-width: 2px;
  }

  .hero-title,
  .section-title,
  .feed-title {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .empty-icon-glow {
    animation: none;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Focus utilities for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Loading state utilities */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary-alpha);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Text utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--color-text-white) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Spacing utilities */
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Print styles */
@media print {
  .dashboard-hero,
  .dashboard-nav,
  .post-engagement,
  .interaction-buttons,
  .hero-actions,
  .section-actions {
    display: none !important;
  }

  .modern-post {
    border: 1px solid #000 !important;
    box-shadow: none !important;
    background: white !important;
    color: black !important;
    break-inside: avoid;
  }

  .post-title-link {
    color: black !important;
    text-decoration: underline !important;
  }
}

</style>
{% endblock %}
