from flask import Flask, flash
from flask_login import current_user
from config import Config
from extensions import db, login_manager
from models import User
from flask_migrate import Migrate
import re
import bleach
import datetime

app = Flask(__name__)
app.config.from_object(Config)
db.init_app(app)
migrate = Migrate(app, db)

login_manager.init_app(app)
login_manager.login_view = "auth.login"

# Add custom template filters
@app.template_filter('nl2br')
def nl2br_filter(text):
    """Convert newlines to <br> tags"""
    if not text:
        return text
    return re.sub(r'\n', '<br>', str(text))

def sanitize_html(content):
    """Sanitize HTML content to prevent XSS attacks"""
    if not content:
        return content

    # Allowed HTML tags and attributes for rich text content
    allowed_tags = [
        'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3',
        'ul', 'ol', 'li', 'blockquote', 'a', 'code', 'pre'
    ]

    allowed_attributes = {
        'a': ['href', 'title'],
        'blockquote': ['cite'],
    }

    # Clean the HTML
    clean_content = bleach.clean(
        content,
        tags=allowed_tags,
        attributes=allowed_attributes,
        strip=True
    )

    return clean_content

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))


# Auto-suspend users whose subscription expired
@app.before_request
def auto_suspend_expired_users():
    try:
        if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            exp = getattr(current_user, 'subscription_expires_at', None)
            if exp and exp < datetime.datetime.utcnow() and not getattr(current_user, 'is_suspended', False):
                current_user.is_suspended = True
                db.session.commit()
                # Best-effort notify; may be suppressed on API calls
                try:
                    flash("Your subscription has expired and your account has been suspended.", "warning")
                except Exception:
                    pass
    except Exception:
        # Do not block requests due to housekeeping; optionally log
        pass

# Import blueprints at the bottom to avoid circular import
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.dashboard import dash_bp
from routes.main import main_bp

app.register_blueprint(auth_bp)
app.register_blueprint(admin_bp, url_prefix="/admin")
app.register_blueprint(dash_bp)
app.register_blueprint(main_bp)

if __name__ == "__main__":
    with app.app_context():
        db.create_all()  # create tables if not exist
    app.run(debug=True)
