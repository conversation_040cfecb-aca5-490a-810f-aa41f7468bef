import datetime
from extensions import db
from flask_login import UserMixin

class User(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    username = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(255), nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    tier = db.Column(db.String(50), default="Initiate")
    is_admin = db.Column(db.Boolean, default=False)
    is_suspended = db.Column(db.Boolean, default=False)
    subscription_expires_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    # Relationships
    posts = db.relationship('Post', backref='author', lazy=True, cascade='all, delete-orphan')
    comments = db.relationship('Comment', backref='author', lazy=True, cascade='all, delete-orphan')
    votes = db.relationship('Vote', backref='user', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<User {self.username}>'


class Application(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), nullable=False)
    username = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(255), nullable=True)

    tier = db.Column(db.String(50))
    reason = db.Column(db.Text)
    status = db.Column(db.String(50), default="pending")
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

class InviteCode(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(255), nullable=False)
    used = db.Column(db.Boolean, default=False)
    used_at = db.Column(db.DateTime)

class Board(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    def __repr__(self):
        return f'<Board {self.name}>'

class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    author_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    board = db.Column(db.String(50), default="General")
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    # Relationships
    comments = db.relationship('Comment', backref='post', lazy=True, cascade='all, delete-orphan')
    votes = db.relationship('Vote', backref='post', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Post {self.title}>'

    @property
    def vote_score(self):
        """Calculate the net vote score (upvotes - downvotes)"""
        upvotes = Vote.query.filter_by(post_id=self.id, vote_type='upvote').count()
        downvotes = Vote.query.filter_by(post_id=self.id, vote_type='downvote').count()
        return upvotes - downvotes

    @property
    def comment_count(self):
        """Get total number of comments (including nested)"""
        return Comment.query.filter_by(post_id=self.id).count()

    def get_user_vote(self, user_id):
        """Get the current user's vote on this post"""
        if not user_id:
            return None
        return Vote.query.filter_by(post_id=self.id, user_id=user_id).first()


class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    author_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    post_id = db.Column(db.Integer, db.ForeignKey("post.id"), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey("comment.id"), nullable=True)  # For nested replies
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    # Self-referential relationship for nested comments
    replies = db.relationship('Comment', backref=db.backref('parent', remote_side=[id]), lazy=True, cascade='all, delete-orphan')
    votes = db.relationship('Vote', backref='comment', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Comment {self.id} by {self.author_id}>'

    @property
    def vote_score(self):
        """Calculate the net vote score (upvotes - downvotes)"""
        upvotes = Vote.query.filter_by(comment_id=self.id, vote_type='upvote').count()
        downvotes = Vote.query.filter_by(comment_id=self.id, vote_type='downvote').count()
        return upvotes - downvotes

    @property
    def reply_count(self):
        """Get number of direct replies"""
        return Comment.query.filter_by(parent_id=self.id).count()

    def get_user_vote(self, user_id):
        """Get the current user's vote on this comment"""
        if not user_id:
            return None
        return Vote.query.filter_by(comment_id=self.id, user_id=user_id).first()

    @property
    def depth(self):
        """Calculate the nesting depth of this comment"""
        if not self.parent_id:
            return 0
        return self.parent.depth + 1


class Vote(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    post_id = db.Column(db.Integer, db.ForeignKey("post.id"), nullable=True)
    comment_id = db.Column(db.Integer, db.ForeignKey("comment.id"), nullable=True)
    vote_type = db.Column(db.String(10), nullable=False)  # 'upvote' or 'downvote'
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    # Ensure a user can only vote once per post/comment
    __table_args__ = (
        db.UniqueConstraint('user_id', 'post_id', name='unique_user_post_vote'),
        db.UniqueConstraint('user_id', 'comment_id', name='unique_user_comment_vote'),
        db.CheckConstraint('(post_id IS NOT NULL AND comment_id IS NULL) OR (post_id IS NULL AND comment_id IS NOT NULL)',
                          name='vote_target_check')
    )

    def __repr__(self):
        target = f"post {self.post_id}" if self.post_id else f"comment {self.comment_id}"
        return f'<Vote {self.vote_type} on {target} by user {self.user_id}>'


class BoardRequest(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    name = db.Column(db.String(100), unique=False, nullable=False)
    description = db.Column(db.Text, nullable=True)
    reason = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(50), default="pending")  # pending, approved, denied
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    def __repr__(self):
        return f"<BoardRequest {self.name} by {self.user_id} ({self.status})>"
