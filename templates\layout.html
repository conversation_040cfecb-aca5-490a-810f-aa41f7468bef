<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ title or "Vallum" }}</title>

  <!-- Preconnect to external domains for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net">

  <!-- CSS Dependencies -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
  <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

  <!-- Main Stylesheet -->
  <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">

  <!-- Meta tags for SEO and social sharing -->
  <meta name="description" content="Vallum - Your gateway to an exclusive, modern community where luxury meets innovation.">
  <meta name="keywords" content="exclusive community, luxury, innovation, networking, membership">
  <meta name="author" content="Vallum">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ title or 'Vallum - Exclusive Community' }}">
  <meta property="og:description" content="Your gateway to an exclusive, modern community where luxury meets innovation.">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="{{ title or 'Vallum - Exclusive Community' }}">
  <meta property="twitter:description" content="Your gateway to an exclusive, modern community where luxury meets innovation.">

  <style>{% block style %}{% endblock %}</style>
</head>
<body>
  <a class="skip-link" href="#main-content">Skip to main content</a>

  <nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
      <a class="navbar-brand" href="/">Vallum</a>

      <!-- Mobile menu toggle button -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Collapsible navbar content -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.index') }}">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('dash.dashboard') }}">Community</a>
            </li>
            {% if current_user.is_admin %}
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  Admin
                </a>
                <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                  <li><a class="dropdown-item" href="{{ url_for('admin.view_applications') }}">Applications</a></li>
                  <li><a class="dropdown-item" href="{{ url_for('admin.view_board_requests') }}">Board Requests</a></li>
                  <li><a class="dropdown-item" href="{{ url_for('admin.members') }}">Members</a></li>
                  <li><a class="dropdown-item" href="{{ url_for('admin.manage_boards') }}">Settings</a></li>
                </ul>
              </li>
            {% endif %}
          {% endif %}
        </ul>
        <div class="navbar-nav ms-auto align-items-center">
          {% if current_user.is_authenticated %}
            <span class="navbar-text text-white me-3 mb-2 mb-lg-0">
              Hello, {{ current_user.username }}
            </span>
            <a class="btn btn-primary btn-sm" href="{{ url_for('auth.logout') }}">Logout</a>
          {% else %}
            <a class="btn btn-primary btn-sm" href="{{ url_for('auth.login') }}">Login</a>
          {% endif %}
        </div>
      </div>
    </div>
  </nav>

  <!-- Main content area -->
  <main id="main-content" class="container mt-5">
    <!-- Breadcrumb navigation -->
    {% block breadcrumb %}
      {% if current_user.is_authenticated %}
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            {% if request.endpoint and request.endpoint.startswith('admin.') %}
              <li class="breadcrumb-item"><a href="{{ url_for('admin.view_applications') }}">Admin</a></li>
              {% if request.endpoint == 'admin.view_applications' %}
                <li class="breadcrumb-item active" aria-current="page">Applications</li>
              {% elif request.endpoint == 'admin.view_board_requests' %}
                <li class="breadcrumb-item active" aria-current="page">Board Requests</li>
              {% elif request.endpoint == 'admin.members' %}
                <li class="breadcrumb-item active" aria-current="page">Members</li>
              {% elif request.endpoint == 'admin.manage_boards' %}
                <li class="breadcrumb-item active" aria-current="page">Settings</li>
              {% endif %}
            {% elif request.endpoint and request.endpoint.startswith('dash.') %}
              <li class="breadcrumb-item active" aria-current="page">Community</li>
            {% endif %}
          </ol>
        </nav>
      {% endif %}
    {% endblock %}

    <!-- Flash messages -->
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        <div class="alert alert-info" role="alert">
          {% for msg in messages %}
            {{ msg }}{% if not loop.last %}<br>{% endif %}
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}

    <!-- Page content -->
    <div class="lux-card">
      {% block content %}{% endblock %}
    </div>
  </main>

  <!-- Footer -->
  <footer>
    <div class="container">
      <p>&copy; 2025 Vallum. All rights reserved.</p>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
  <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

  <!-- Custom JavaScript for enhanced interactions -->
  <script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Enhanced form validation feedback
    document.querySelectorAll('form').forEach(form => {
      form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
          if (!field.value.trim()) {
            field.style.borderColor = 'var(--color-primary)';
            isValid = false;
          } else {
            field.style.borderColor = '';
          }
        });

        if (!isValid) {
          e.preventDefault();
        }
      });
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(() => {
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(alert => {
        alert.style.transition = 'opacity 0.5s ease';
        alert.style.opacity = '0';
        setTimeout(() => alert.remove(), 500);
      });
    }, 5000);
  </script>
  <script>
    // Lightweight custom popup system (used instead of window.alert)
    (function(){
      function ensurePopupStyles(){
        if(document.getElementById('popup-styles')) return;
        const style = document.createElement('style');
        style.id = 'popup-styles';
        style.textContent = `
          .custom-popup{position:fixed;right:20px;top:20px;z-index:1100;min-width:300px;max-width:420px;background:var(--color-bg-tertiary);
            border:1px solid var(--color-border);border-radius:12px;box-shadow:var(--shadow-lg);padding:12px 16px;color:var(--color-text-primary);
            display:flex;gap:12px;align-items:flex-start;opacity:0;transform:translateY(-8px);transition:all .25s ease}
          .custom-popup.show{opacity:1;transform:translateY(0)}
          .custom-popup .icon{font-size:1.25rem;line-height:1;color:var(--color-primary)}
          .custom-popup.success .icon{color:var(--color-success)}
          .custom-popup.error .icon{color:var(--color-error)}
          .custom-popup.warning .icon{color:var(--color-warning)}
          .custom-popup .content{flex:1}
          .custom-popup .title{font-weight:600;margin:0 0 4px 0}
          .custom-popup .close{background:transparent;border:none;color:var(--color-text-tertiary);cursor:pointer}
          @media (max-width:576px){.custom-popup{left:12px;right:12px;min-width:auto}}
        `;
        document.head.appendChild(style);
      }
      window.showPopup = function(message, type='info', title){
        try{
          ensurePopupStyles();
          const el = document.createElement('div');
          el.className = `custom-popup ${type}`;
          const icon = type==='success'?'bi-check-circle':type==='error'?'bi-x-circle':type==='warning'?'bi-exclamation-triangle':'bi-info-circle';
          el.innerHTML = `
            <div class="icon"><i class="bi ${icon}"></i></div>
            <div class="content">
              ${title ? `<div class="title">${title}</div>` : ''}
              <div class="message">${message}</div>
            </div>
            <button class="close" aria-label="Close" title="Close" onclick="this.parentElement.remove()">
              <i class="bi bi-x"></i>
            </button>
          `;
          document.body.appendChild(el);
          requestAnimationFrame(()=>el.classList.add('show'));
          setTimeout(()=>{ if(el && el.parentElement){ el.classList.remove('show'); setTimeout(()=>el.remove(), 250); } }, 5000);
        }catch(e){ console.warn('Popup error', e); }
      };
      // Replace native alerts with custom popup for better UX
      const nativeAlert = window.alert;
      window.alert = function(msg){ try{ window.showPopup(String(msg),'info'); } catch(e){ nativeAlert(msg); } };
    })();
  </script>

</body>
</html>
